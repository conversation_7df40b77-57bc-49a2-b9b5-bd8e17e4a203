<?php
/**
 * Homepage Template for Petting Zoo Directory
 */

get_header(); ?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">
        
        <!-- Enhanced Hero Section - Trust & Family Focus -->
        <section class="hero-section">
            <div class="hero-background">
                <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/placeholder.jpg" alt="Family enjoying petting zoo" class="hero-image">
                <div class="hero-overlay"></div>
            </div>
            <div class="hero-content">
                <div class="container">
                    <h1>Discover the Best Petting Zoos Near You</h1>
                    <p>Find amazing petting zoos, animal farms, and interactive experiences perfect for families. Create lasting memories with hands-on animal encounters that kids and dads love.</p>

                    <!-- Enhanced Zoo Finder Tool -->
                    <div class="zoo-finder">
                        <h3>🔍 Discover Petting Zoos Near You</h3>
                        <form id="zoo-finder-form">
                            <select id="finder-location" name="location" aria-label="Select your city">
                                <option value="">🏙️ Select Your City</option>
                                <?php
                                $locations = get_terms(array(
                                    'taxonomy' => 'location',
                                    'hide_empty' => false,
                                    'parent' => 0
                                ));

                                foreach ($locations as $state) {
                                    $cities = get_terms(array(
                                        'taxonomy' => 'location',
                                        'hide_empty' => false,
                                        'parent' => $state->term_id
                                    ));

                                    if (!empty($cities)) {
                                        echo '<optgroup label="' . esc_attr($state->name) . '">';
                                        foreach ($cities as $city) {
                                            echo '<option value="' . esc_attr($city->slug) . '">' . esc_html($city->name) . '</option>';
                                        }
                                        echo '</optgroup>';
                                    }
                                }
                                ?>
                            </select>

                            <select id="finder-animal" name="animal" aria-label="Select animal type">
                                <option value="">🐾 Any Animal Type</option>
                                <?php
                                $animals = get_terms(array(
                                    'taxonomy' => 'animal_type',
                                    'hide_empty' => false
                                ));

                                foreach ($animals as $animal) {
                                    echo '<option value="' . esc_attr($animal->slug) . '">' . esc_html($animal->name) . '</option>';
                                }
                                ?>
                            </select>

                            <button type="submit" class="btn">🔍 Find Petting Zoos</button>
                            <button type="button" id="find-near-me" class="btn btn-secondary">📍 Find Near Me</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Popular Cities Section -->
        <section class="section popular-cities-section">
            <div class="container">
                <h2 class="section-title">Popular Cities</h2>
                <p class="section-subtitle">Explore petting zoos in these popular cities across the United States</p>

                <div class="state-cards-grid">
                    <?php
                    // Read and parse the CSV file
                    $csv_file = get_template_directory() . '/../../../uploads/2025/06/city-state.csv';
                    $states_data = array();

                    if (file_exists($csv_file)) {
                        $handle = fopen($csv_file, 'r');
                        $header = fgetcsv($handle); // Skip header row

                        while (($data = fgetcsv($handle)) !== FALSE) {
                            if (!empty($data[0]) && !empty($data[2])) {
                                $city = $data[0];
                                $state = $data[2];

                                if (!isset($states_data[$state])) {
                                    $states_data[$state] = array();
                                }
                                $states_data[$state][] = $city;
                            }
                        }
                        fclose($handle);
                    }

                    // Sort states alphabetically and limit cities per state
                    ksort($states_data);
                    $displayed_states = 0;
                    $max_states = 12; // Display 12 states in 3 columns, 4 rows

                    foreach ($states_data as $state => $cities) {
                        if ($displayed_states >= $max_states) break;

                        // Limit to 6 cities per state for better display
                        $cities = array_slice($cities, 0, 6);
                        ?>
                        <div class="state-card">
                            <h3 class="state-title">Petting Zoos in <?php echo esc_html($state); ?></h3>
                            <div class="cities-list">
                                <?php foreach ($cities as $city) : ?>
                                    <a href="/city/<?php echo sanitize_title($city); ?>-<?php echo sanitize_title($state); ?>/" class="city-link">
                                        <?php echo esc_html($city); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php
                        $displayed_states++;
                    }
                    ?>
                </div>
            </div>
        </section>

        <!-- Explore by Animal Section -->
        <section class="section" style="background: #f8f9fa;">
            <div class="container">
                <h2 class="section-title">Explore by Animal Type</h2>
                <p class="section-subtitle">Find petting zoos that feature your favorite animals</p>
                
                <div class="animals-grid">
                    <?php
                    $featured_animals = array(
                        'goats' => '🐐',
                        'sheep' => '🐑',
                        'pigs' => '🐷',
                        'rabbits' => '🐰',
                        'chickens' => '🐔',
                        'horses' => '🐴',
                        'cows' => '🐄',
                        'llamas' => '🦙',
                        'alpacas' => '🦙',
                        'capybaras' => '🐹',
                        'sloths' => '🦥',
                        'miniature-donkeys' => '🫏'
                    );
                    
                    $animal_terms = get_terms(array(
                        'taxonomy' => 'animal_type',
                        'hide_empty' => true,
                        'number' => 12
                    ));
                    
                    foreach ($animal_terms as $animal) {
                        $animal_url = get_term_link($animal);
                        $icon = isset($featured_animals[$animal->slug]) ? $featured_animals[$animal->slug] : '🐾';
                        ?>
                        <a href="<?php echo esc_url($animal_url); ?>" class="animal-card">
                            <div class="animal-icon"><?php echo $icon; ?></div>
                            <h4><?php echo esc_html($animal->name); ?></h4>
                            <div class="zoo-count"><?php echo $animal->count; ?> Location<?php echo $animal->count !== 1 ? 's' : ''; ?></div>
                        </a>
                        <?php
                    }
                    ?>
                </div>
            </div>
        </section>

        <!-- Plan Your Visit Section -->
        <section class="section">
            <div class="container">
                <h2 class="section-title">Plan Your Perfect Petting Zoo Visit</h2>
                
                <div class="zoo-grid">
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🎯 Choose Your Experience</h3>
                            <p>From traditional farm settings to exotic animal encounters, find the perfect petting zoo that matches your family's interests and comfort level.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🎉 Special Events</h3>
                            <p>Many petting zoos offer birthday parties, educational field trips, and seasonal events. Check our event listings to make your visit extra special.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🍎 Feeding Experiences</h3>
                            <p>Most petting zoos offer animal feeding opportunities. Some provide feed on-site, while others allow you to bring approved snacks for the animals.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🚗 Amenities</h3>
                            <p>Look for petting zoos with picnic areas, playgrounds, gift shops, and ample parking to make your family outing comfortable and enjoyable.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Enhanced Why Dads Love It Section - Emotional Connection -->
        <section class="section" style="background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);">
            <div class="container">
                <h2 class="section-title">Why Dads Love Petting Zoo Adventures</h2>
                <p class="section-subtitle">Real experiences that create lasting father-child memories</p>

                <div class="zoo-grid">
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>👨‍👧‍👦 Quality Bonding Time</h3>
                            <p>Create lasting memories with your children while exploring and learning about animals together. Watch their faces light up when they feed a goat or pet a gentle sheep.</p>
                            <div class="features">
                                <span class="feature-tag">No Distractions</span>
                                <span class="feature-tag">Shared Wonder</span>
                            </div>
                        </div>
                    </div>

                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🎯 Easy Planning</h3>
                            <p>Most petting zoos are simple day trips that don't require extensive planning. Just show up and enjoy a few hours of family fun without the stress.</p>
                            <div class="features">
                                <span class="feature-tag">Low Stress</span>
                                <span class="feature-tag">Flexible Timing</span>
                            </div>
                        </div>
                    </div>

                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>📱 Screen-Free Fun</h3>
                            <p>Give the kids a break from devices while engaging with real animals and nature. It's educational entertainment that actually teaches life skills.</p>
                            <div class="features">
                                <span class="feature-tag">Real Interaction</span>
                                <span class="feature-tag">Learning Fun</span>
                            </div>
                        </div>
                    </div>

                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>💰 Budget-Friendly</h3>
                            <p>Petting zoos offer great value for families, with most charging reasonable admission fees for hours of entertainment and memories.</p>
                            <div class="features">
                                <span class="feature-tag">Great Value</span>
                                <span class="feature-tag">All Ages</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section faq-section">
            <div class="container">
                <h2 class="section-title">Frequently Asked Questions</h2>
                
                <div class="faq-item">
                    <div class="faq-question">Can I bring my own food to a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Most petting zoos allow you to bring your own food for picnicking, but policies vary. Some have on-site cafes or snack bars. Always check with the specific petting zoo before your visit.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">What should I wear to a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Wear comfortable, closed-toe shoes and clothes you don't mind getting dirty. Avoid loose jewelry and bring hand sanitizer. Many petting zoos are outdoors, so dress for the weather.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Are petting zoos safe for young children?</div>
                    <div class="faq-answer">
                        <p>Yes, reputable petting zoos prioritize safety with gentle animals, proper supervision, and safety guidelines. Always supervise young children and follow the zoo's rules for interacting with animals.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Do I need to make reservations?</div>
                    <div class="faq-answer">
                        <p>While many petting zoos accept walk-ins, it's always best to call ahead, especially for larger groups, special events, or during peak seasons like spring and summer.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">What's the best time to visit a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Animals are typically most active in the morning and late afternoon. Weekdays are usually less crowded than weekends. Spring and fall offer the most comfortable weather for outdoor visits.</p>
                    </div>
                </div>
            </div>
        </section>

    </main>
</div>

<?php get_footer(); ?>
